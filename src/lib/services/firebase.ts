// Firebase Firestore service for queue management

import {
  collection,
  doc,
  addDoc,
  updateDoc,
  deleteDoc,
  getDocs,
  getDoc,
  query,
  where,
  orderBy,
  limit,
  onSnapshot,
  serverTimestamp,
  writeBatch,
  Timestamp
} from 'firebase/firestore'
import { getFirebaseDb } from '@/lib/firebase/config'
import { Queue, QueueState, QueueMetadata } from '@/lib/types/queue'
import { prepareForFirebase, validateFirebaseData, timestampToMillis } from '@/lib/utils/firebase'
import { VideoMetadata } from '@/lib/types/video'

export class FirebaseService {
  private getDb() {
    const db = getFirebaseDb()
    if (!db) {
      console.warn('Firebase not initialized. Queue persistence will not work.')
    }
    return db
  }

  // Unified Queue Management
  async saveQueue(userId: string, queueData: QueueState, metadata: Partial<QueueMetadata>, isPublic = false): Promise<string | null> {
    const db = this.getDb()
    if (!db) return null

    try {
      const queueRef = collection(db, 'queues')
      const queueDoc = {
        userId,
        queueData,
        metadata: {
          ...metadata,
          videoCount: queueData.items.length,
          totalDuration: queueData.items.reduce((total, item) => total + (item.duration || 0), 0),
          firstVideoThumbnail: queueData.items[0]?.thumbnail || '',
          createdAt: Date.now(),
          lastModified: Date.now(),
          viewCount: 0,
          isPublic,
        },
        isPublic,
        createdAt: serverTimestamp(),
        lastModified: serverTimestamp(),
      }

      // Clean the document data to remove undefined values
      const cleanedQueueDoc = prepareForFirebase(queueDoc)

      // Validate the data before saving
      const validation = validateFirebaseData(cleanedQueueDoc, 'queueDoc')
      if (!validation.isValid) {
        console.error('❌ Invalid data for Firebase:', validation.errors)
        throw new Error(`Invalid data: ${validation.errors.join(', ')}`)
      }

      const docRef = await addDoc(queueRef, cleanedQueueDoc)
      console.log(`✅ Queue saved (${isPublic ? 'public' : 'private'}):`, docRef.id)
      return docRef.id
    } catch (error) {
      console.error('❌ Error saving queue:', error)
      return null
    }
  }

  async getQueue(queueId: string, currentUserId?: string): Promise<Queue | null> {
    const db = this.getDb()
    if (!db) return null

    try {
      const queueRef = doc(db, 'queues', queueId)
      const queueSnap = await getDoc(queueRef)

      if (!queueSnap.exists()) {
        console.warn('Queue not found:', queueId)
        return null
      }

      const data = queueSnap.data()
      const queue = {
        id: queueSnap.id,
        ...data,
        createdAt: timestampToMillis(data.createdAt),
        lastModified: timestampToMillis(data.lastModified),
      } as Queue

      // Check access: owner OR public
      if (currentUserId && queue.userId !== currentUserId && !queue.isPublic) {
        console.warn('Access denied to private queue:', queueId)
        return null
      }

      console.log('✅ Queue loaded:', queueId)
      return queue
    } catch (error) {
      console.error('❌ Error loading queue:', error)
      return null
    }
  }

  async getUserQueues(userId: string, preferCache: boolean = false): Promise<Queue[]> {
    const db = this.getDb()
    if (!db) return []

    try {
      // Simple query without orderBy to avoid index requirement and improve offline performance
      const q = query(
        collection(db, 'queues'),
        where('userId', '==', userId)
      )

      // Use getDocs with source preference for better offline support
      const querySnapshot = await getDocs(q)
      const queues: Queue[] = []
      const isFromCache = querySnapshot.metadata.fromCache

      console.log(`📋 Fetched ${querySnapshot.size} user queues from ${isFromCache ? 'cache' : 'server'}`)

      querySnapshot.forEach((doc) => {
        const data = doc.data()
        queues.push({
          id: doc.id,
          ...data,
          createdAt: timestampToMillis(data.createdAt),
          lastModified: timestampToMillis(data.lastModified),
        } as Queue)
      })

      // Sort by lastModified in JavaScript instead of Firestore
      queues.sort((a, b) => b.lastModified - a.lastModified)

      console.log(`✅ Loaded ${queues.length} user queues from ${isFromCache ? 'cache' : 'server'}`)
      return queues
    } catch (error) {
      console.error('❌ Error fetching user queues:', error)
      // If there's an error and we're offline, this might be expected
      // The real-time listener should handle offline scenarios better
      return []
    }
  }

  async updateQueue(queueId: string, updates: Partial<Queue>, currentUserId: string): Promise<boolean> {
    const db = this.getDb()
    if (!db) return false

    try {
      // First check if user owns this queue
      const queueRef = doc(db, 'queues', queueId)
      const queueSnap = await getDoc(queueRef)

      if (!queueSnap.exists()) {
        console.warn('Queue not found for update:', queueId)
        return false
      }

      const queueData = queueSnap.data() as Queue
      if (queueData.userId !== currentUserId) {
        console.warn('Access denied: User does not own queue:', queueId)
        return false
      }

      const updateData = {
        ...updates,
        lastModified: serverTimestamp(),
      }

      // Clean the update data to remove undefined values
      const cleanedUpdateData = prepareForFirebase(updateData)

      await updateDoc(queueRef, cleanedUpdateData)
      console.log('✅ Queue updated:', queueId)
      return true
    } catch (error) {
      console.error('❌ Error updating queue:', error)
      return false
    }
  }



  async duplicateQueue(queueId: string, userId: string, newTitle?: string): Promise<string | null> {
    const db = this.getDb()
    if (!db) return null

    try {
      // Get the original queue from unified collection
      const originalQueue = await this.getQueue(queueId, userId)

      if (!originalQueue) {
        throw new Error('Original queue not found')
      }

      // Create new queue data using the unified saveQueue method
      const duplicatedMetadata = {
        title: newTitle || `${originalQueue.metadata.title} (Copy)`,
        description: originalQueue.metadata.description,
        tags: originalQueue.metadata.tags,
      }

      const newQueueId = await this.saveQueue(userId, originalQueue.queueData, duplicatedMetadata, false)

      if (newQueueId) {
        console.log('✅ Queue duplicated:', newQueueId)
        return newQueueId
      } else {
        throw new Error('Failed to save duplicated queue')
      }
    } catch (error) {
      console.error('❌ Error duplicating queue:', error)
      return null
    }
  }

  async deleteQueue(queueId: string, currentUserId: string): Promise<boolean> {
    const db = this.getDb()
    if (!db) return false

    try {
      // First check if user owns this queue
      const queueRef = doc(db, 'queues', queueId)
      const queueSnap = await getDoc(queueRef)

      if (!queueSnap.exists()) {
        console.warn('Queue not found for deletion:', queueId)
        return false
      }

      const queueData = queueSnap.data() as Queue
      if (queueData.userId !== currentUserId) {
        console.warn('Access denied: User does not own queue:', queueId)
        return false
      }

      await deleteDoc(queueRef)
      console.log('✅ Queue deleted:', queueId)
      return true
    } catch (error) {
      console.error('❌ Error deleting queue:', error)
      return false
    }
  }

  // Public Queues

  async shareCurrentQueue(userId: string, queueData: QueueState, title: string): Promise<string | null> {
    const db = this.getDb()
    if (!db) return null

    try {
      // Create video IDs string for comparison
      const videoIds = queueData.items.map(item => item.id).join('|')
      console.log('🔍 DEBUG: Current queue video IDs:', videoIds)
      console.log('🔍 DEBUG: Current queue has', queueData.items.length, 'videos')
      console.log('🔍 DEBUG: User ID:', userId)

      // Check if a public queue with the same video IDs already exists for this user
      const q = query(
        collection(db, 'queues'),
        where('userId', '==', userId),
        where('isPublic', '==', true)
      )

      console.log('🔍 DEBUG: Querying existing public queues...')
      const querySnapshot = await getDocs(q)
      console.log('🔍 DEBUG: Found', querySnapshot.docs.length, 'existing public queues for this user')

      for (const doc of querySnapshot.docs) {
        const existingQueue = doc.data()
        console.log('🔍 DEBUG: Checking queue', doc.id)
        console.log('🔍 DEBUG: Queue data structure:', {
          hasQueueData: !!existingQueue.queueData,
          hasItems: !!existingQueue.queueData?.items,
          itemsLength: existingQueue.queueData?.items?.length || 0
        })

        if (!existingQueue.queueData?.items) {
          console.log('⚠️ DEBUG: Queue has no items, skipping')
          continue
        }

        const existingVideoIds = existingQueue.queueData.items.map((item: any) => item.id).join('|')
        console.log('🔍 DEBUG: Existing queue video IDs:', existingVideoIds)
        console.log('🔍 DEBUG: Comparing:', {
          current: videoIds,
          existing: existingVideoIds,
          match: existingVideoIds === videoIds
        })

        if (existingVideoIds === videoIds) {
          console.log('✅ DEBUG: Found matching queue!', doc.id)
          return doc.id
        }
      }

      console.log('📝 DEBUG: No matching queue found, creating new one')

      // Create a new public queue directly
      const publicQueueRef = collection(db, 'queues')

      // Clean up the queue data for sharing (remove sharing state and reset playback state)
      const cleanedQueueData = {
        ...queueData,
        currentIndex: 0,
        isPlaying: false,
        timestamp: Date.now()
      }

      // Remove undefined fields (Firebase doesn't allow them)
      if ('sharedPublicId' in cleanedQueueData) {
        delete cleanedQueueData.sharedPublicId
      }

      const publicQueueDoc = {
        queueData: cleanedQueueData,
        metadata: {
          title,
          description: `Queue with ${queueData.items.length} videos`,
          videoCount: queueData.items.length,
          totalDuration: queueData.items.reduce((total, item) => total + (item.duration || 0), 0),
          firstVideoThumbnail: queueData.items[0]?.thumbnail || '',
          createdAt: Date.now(),
          lastModified: Date.now(),
          viewCount: 0,
          isPublic: true,
        },
        isPublic: true,
        createdAt: serverTimestamp(),
        lastModified: serverTimestamp(),
        createdBy: userId,
      }

      console.log('💾 Creating new public queue directly')
      const docRef = await addDoc(publicQueueRef, publicQueueDoc)
      console.log('✅ Public queue created with ID:', docRef.id)

      return docRef.id
    } catch (error) {
      console.error('❌ Error sharing current queue:', error)
      return null
    }
  }

  async getPublicQueues(limitCount: number = 20): Promise<Queue[]> {
    const db = this.getDb()
    if (!db) return []

    try {
      console.log('🔍 Fetching public queues from Firestore...')

      // First, try to get all documents to see what's in the collection
      const allDocsQuery = query(collection(db, 'queues'), limit(50))
      const allDocsSnapshot = await getDocs(allDocsQuery)

      console.log(`📊 Found ${allDocsSnapshot.size} total documents in 'queues' collection`)

      if (allDocsSnapshot.size === 0) {
        console.log('📭 No documents found in queues collection')
        return []
      }

      // Log the first few documents to understand the structure
      allDocsSnapshot.docs.slice(0, 3).forEach((doc, index) => {
        const data = doc.data()
        console.log(`📄 Document ${index + 1}:`, {
          id: doc.id,
          isPublic: data.isPublic,
          metadataIsPublic: data.metadata?.isPublic,
          hasQueueData: !!data.queueData,
          hasMetadata: !!data.metadata,
          createdBy: data.createdBy,
          structure: Object.keys(data)
        })
        console.log(`📄 Full data for Document ${index + 1}:`, data)
      })

      // The indexed query isn't working, so let's use the manual filtering approach
      // Get all documents and filter manually
      const q = query(
        collection(db, 'queues'),
        limit(limitCount * 3) // Get more to filter manually
      )
      const allDocs = await getDocs(q)
      console.log(`📋 Retrieved ${allDocs.size} documents for manual filtering`)

      // Filter manually for public queues
      const filteredDocs: any[] = []
      allDocs.forEach((doc) => {
        const data = doc.data()
        console.log(`🔍 Checking document ${doc.id}:`, {
          isPublic: data.isPublic,
          metadataIsPublic: data.metadata?.isPublic,
          startsWithPersonal: doc.id.startsWith('personal_')
        })

        // Include documents that are public queues:
        // 1. Documents with queue_ prefix (original public queues)
        // 2. Documents explicitly marked as public
        if (doc.id.startsWith('queue_') ||
            data.isPublic === true ||
            data.metadata?.isPublic === true) {
          console.log(`✅ Including document ${doc.id} as public queue`)
          filteredDocs.push(doc)
        } else {
          console.log(`❌ Excluding document ${doc.id} - not a public queue`)
        }
      })

      console.log(`📋 Filtered to ${filteredDocs.length} public queues`)

      // Create a mock QuerySnapshot-like object
      const querySnapshot = {
        size: filteredDocs.length,
        forEach: (callback: any) => filteredDocs.forEach(callback)
      } as any

      const queues: Queue[] = []

      querySnapshot.forEach((doc: any) => {
        const data = doc.data()
        console.log('🔍 Processing document:', doc.id, data)

        queues.push({
          id: doc.id,
          ...data,
          createdAt: timestampToMillis(data.createdAt),
          lastModified: timestampToMillis(data.lastModified),
        } as Queue)
      })

      // Sort manually if we used the simple query
      if (queues.length > 0) {
        queues.sort((a, b) => b.lastModified - a.lastModified)
      }

      console.log(`✅ Returning ${queues.length} public queues`)
      return queues
    } catch (error) {
      console.error('❌ Error fetching public queues:', error)
      console.error('Error details:', error)
      return []
    }
  }

  async getPublicQueue(queueId: string): Promise<Queue | null> {
    const db = this.getDb()
    if (!db) {
      console.warn('❌ Firebase database not available for getPublicQueue')
      return null
    }

    try {
      console.log('🔍 Fetching public queue:', queueId)
      const queueRef = doc(db, 'queues', queueId)
      const queueSnap = await getDoc(queueRef)

      if (!queueSnap.exists()) {
        console.warn('❌ Public queue not found:', queueId)
        return null
      }

      const data = queueSnap.data()
      console.log('📄 Queue data retrieved:', {
        id: queueSnap.id,
        isPublic: data.isPublic,
        hasQueueData: !!data.queueData,
        title: data.metadata?.title
      })

      // Verify this is actually a public queue
      if (!data.isPublic) {
        console.warn('❌ Queue is not public:', queueId)
        return null
      }

      const queue = {
        id: queueSnap.id,
        ...data,
        createdAt: timestampToMillis(data.createdAt),
        lastModified: timestampToMillis(data.lastModified),
      } as Queue

      console.log('✅ Public queue loaded successfully:', queue.metadata?.title)
      return queue
    } catch (error) {
      console.error('❌ Error fetching public queue:', error)
      console.error('Error details:', error)
      return null
    }
  }

  async searchPublicQueues(searchTerm: string, limitCount: number = 20): Promise<Queue[]> {
    const db = this.getDb()
    if (!db) return []

    try {
      console.log('🔍 Searching public queues for:', searchTerm)

      // Note: Firestore doesn't support full-text search natively
      // Get all documents and filter manually (same as getPublicQueues)
      const q = query(
        collection(db, 'queues'),
        limit(limitCount * 5) // Get more to filter by search term
      )

      const querySnapshot = await getDocs(q)
      const queues: Queue[] = []

      querySnapshot.forEach((doc) => {
        const data = doc.data()

        // Include documents that are marked as public OR have queue_ prefix (legacy public queues)
        if (data.isPublic === true ||
            data.metadata?.isPublic === true ||
            doc.id.startsWith('queue_')) {

          const queue = {
            id: doc.id,
            ...data,
            createdAt: timestampToMillis(data.createdAt),
            lastModified: timestampToMillis(data.lastModified),
          } as Queue

          // Filter by search term
          if (
            queue.metadata?.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
            queue.metadata?.description?.toLowerCase().includes(searchTerm.toLowerCase())
          ) {
            queues.push(queue)
          }
        }
      })

      console.log(`🔍 Found ${queues.length} matching public queues`)
      return queues.slice(0, limitCount)
    } catch (error) {
      console.error('❌ Error searching public queues:', error)
      return []
    }
  }

  // Real-time listeners
  subscribeToPersonalQueues(userId: string, callback: (queues: Queue[]) => void): () => void {
    const db = this.getDb()
    if (!db) return () => {}

    // Subscribe to ALL user queues (both public and private) for personal view
    const q = query(
      collection(db, 'queues'),
      where('userId', '==', userId)
      // Note: Removed orderBy to avoid index requirement and improve offline performance
      // We'll sort in JavaScript instead
    )

    return onSnapshot(q, { includeMetadataChanges: true }, (querySnapshot) => {
      const queues: Queue[] = []
      const isFromCache = querySnapshot.metadata.fromCache

      console.log(`📋 Personal queues update: ${querySnapshot.size} queues from ${isFromCache ? 'cache' : 'server'}`)

      querySnapshot.forEach((doc) => {
        const data = doc.data()
        queues.push({
          id: doc.id,
          ...data,
          createdAt: timestampToMillis(data.createdAt),
          lastModified: timestampToMillis(data.lastModified),
        } as Queue)
      })

      // Sort by lastModified in JavaScript (newest first)
      queues.sort((a, b) => b.lastModified - a.lastModified)

      callback(queues)
    }, (error) => {
      console.error('❌ Error in personal queues listener:', error)
      // On error, try to provide cached data if available
      callback([])
    })
  }

  // Enhanced version that provides metadata about data source
  subscribeToPersonalQueuesWithMetadata(
    userId: string,
    callback: (queues: Queue[], metadata: { fromCache: boolean; hasPendingWrites: boolean }) => void
  ): () => void {
    const db = this.getDb()
    if (!db) return () => {}

    // Subscribe to ALL user queues (both public and private) for personal view
    const q = query(
      collection(db, 'queues'),
      where('userId', '==', userId)
      // Note: Removed orderBy to avoid index requirement and improve offline performance
      // We'll sort in JavaScript instead
    )

    return onSnapshot(q, { includeMetadataChanges: true }, (querySnapshot) => {
      const queues: Queue[] = []
      const metadata = {
        fromCache: querySnapshot.metadata.fromCache,
        hasPendingWrites: querySnapshot.metadata.hasPendingWrites
      }

      console.log(`📋 Personal queues update: ${querySnapshot.size} queues from ${metadata.fromCache ? 'cache' : 'server'}${metadata.hasPendingWrites ? ' (with pending writes)' : ''}`)

      querySnapshot.forEach((doc) => {
        const data = doc.data()
        queues.push({
          id: doc.id,
          ...data,
          createdAt: timestampToMillis(data.createdAt),
          lastModified: timestampToMillis(data.lastModified),
        } as Queue)
      })

      // Sort by lastModified in JavaScript (newest first)
      queues.sort((a, b) => b.lastModified - a.lastModified)

      callback(queues, metadata)
    }, (error) => {
      console.error('❌ Error in personal queues listener:', error)
      // On error, try to provide cached data if available
      callback([], { fromCache: true, hasPendingWrites: false })
    })
  }

  subscribeToPublicQueues(callback: (queues: Queue[]) => void, limitCount: number = 20): () => void {
    const db = this.getDb()
    if (!db) return () => {}

    const q = query(
      collection(db, 'queues'),
      where('isPublic', '==', true),
      orderBy('lastModified', 'desc'),
      limit(limitCount)
    )

    return onSnapshot(q, (querySnapshot) => {
      const queues: Queue[] = []
      querySnapshot.forEach((doc) => {
        const data = doc.data()

        // Skip personal queues that ended up in wrong collection
        if (doc.id.startsWith('personal_')) {
          return
        }

        queues.push({
          id: doc.id,
          ...data,
          createdAt: timestampToMillis(data.createdAt),
          lastModified: timestampToMillis(data.lastModified),
        } as Queue)
      })
      callback(queues)
    })
  }



  // Account linking and data migration
  async migrateAnonymousUserData(anonymousUid: string, authenticatedUid: string): Promise<void> {
    const db = this.getDb()
    if (!db) {
      console.error('❌ Database not available for migration')
      return
    }

    try {
      console.log(`🔄 Starting data migration from ${anonymousUid} to ${authenticatedUid}`)

      // Create a batch for atomic operations
      const batch = writeBatch(db)
      let migrationCount = 0

      // Migrate queues from both old personal_queues and unified queues collections
      const personalQueuesQuery = query(
        collection(db, 'personal_queues'),
        where('userId', '==', anonymousUid)
      )

      const unifiedQueuesQuery = query(
        collection(db, 'queues'),
        where('userId', '==', anonymousUid)
      )

      const [personalQueuesSnapshot, unifiedQueuesSnapshot] = await Promise.all([
        getDocs(personalQueuesQuery),
        getDocs(unifiedQueuesQuery)
      ])

      // Migrate from old personal_queues collection
      for (const docSnapshot of personalQueuesSnapshot.docs) {
        const queueData = docSnapshot.data() as Queue

        // Create new document with authenticated user ID in unified collection
        const newQueueRef = doc(collection(db, 'queues'))
        const updatedQueueData = {
          ...queueData,
          userId: authenticatedUid,
          lastModified: serverTimestamp(),
          // Add migration metadata
          migratedFrom: anonymousUid,
          migratedAt: serverTimestamp()
        }

        // Clean the data before saving
        const cleanedData = prepareForFirebase(updatedQueueData)

        // Add to batch
        batch.set(newQueueRef, cleanedData)

        // Mark old document for deletion
        batch.delete(docSnapshot.ref)

        migrationCount++
        console.log(`📦 Migrating personal queue from old collection: ${docSnapshot.id}`)
      }

      // Update existing queues in unified collection
      for (const docSnapshot of unifiedQueuesSnapshot.docs) {
        const queueData = docSnapshot.data() as Queue

        const updatedQueueData = {
          ...queueData,
          userId: authenticatedUid,
          lastModified: serverTimestamp(),
          // Add migration metadata
          migratedFrom: anonymousUid,
          migratedAt: serverTimestamp()
        }

        // Clean the data before saving
        const cleanedData = prepareForFirebase(updatedQueueData)

        // Update existing document
        batch.update(docSnapshot.ref, cleanedData)

        migrationCount++
        console.log(`📦 Migrating queue from unified collection: ${docSnapshot.id}`)
      }

      // Migrate any user profile data if it exists
      const userProfileRef = doc(db, 'users', anonymousUid)
      const userProfileSnapshot = await getDoc(userProfileRef)

      if (userProfileSnapshot.exists()) {
        const profileData = userProfileSnapshot.data()
        const newProfileRef = doc(db, 'users', authenticatedUid)

        // Merge with existing profile data if any
        const existingProfileSnapshot = await getDoc(newProfileRef)
        let mergedProfileData = profileData

        if (existingProfileSnapshot.exists()) {
          const existingData = existingProfileSnapshot.data()
          mergedProfileData = {
            ...profileData,
            ...existingData, // Existing data takes precedence
            migratedFrom: anonymousUid,
            migratedAt: serverTimestamp()
          }
        } else {
          mergedProfileData = {
            ...profileData,
            migratedFrom: anonymousUid,
            migratedAt: serverTimestamp()
          }
        }

        const cleanedProfileData = prepareForFirebase(mergedProfileData)
        batch.set(newProfileRef, cleanedProfileData)
        batch.delete(userProfileRef)

        console.log(`👤 Migrating user profile data`)
      }

      // Execute the batch
      if (migrationCount > 0 || userProfileSnapshot.exists()) {
        await batch.commit()
        console.log(`✅ Migration completed: ${migrationCount} queues migrated`)
      } else {
        console.log('ℹ️ No data to migrate')
      }

    } catch (error: any) {
      console.error('❌ Error during data migration:', error)

      // Handle permission errors gracefully - this is expected when trying to access
      // anonymous user data with a different authenticated user
      if (error?.code === 'permission-denied') {
        console.log('ℹ️ Permission denied during migration - this is expected when anonymous data cannot be accessed')
        console.log('ℹ️ User can continue without data migration')
        return // Don't throw error, just skip migration
      }

      // For other errors, still throw to handle them appropriately
      throw error
    }
  }
}

// Export singleton instance
export const firebaseService = new FirebaseService()


